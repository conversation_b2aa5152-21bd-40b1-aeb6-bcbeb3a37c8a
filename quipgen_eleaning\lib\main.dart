import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cupertino_icons/cupertino_icons.dart';
import 'package:quipgen_eleaning/firebase_options.dart';
import 'package:quipgen_eleaning/view/auth/auth_wrapper.dart';
import 'package:quipgen_eleaning/view/auth/profile_screen.dart';
import 'package:quipgen_eleaning/view/home/<USER>';
import 'package:quipgen_eleaning/view/onboarding/onboarding_screen.dart';
import 'package:quipgen_eleaning/view/login/signup_screen.dart';
import 'package:quipgen_eleaning/view/login/login_screen.dart';
import 'package:quipgen_eleaning/view/login/phone_signup_screen.dart';
import 'package:quipgen_eleaning/view/course/course_details_screen.dart';
import 'package:quipgen_eleaning/view/course/enrollment_screen.dart';
import 'package:quipgen_eleaning/view/course/checkout_screen.dart';
import 'package:quipgen_eleaning/view/test/backend_test_screen.dart';

// import 'package:quipgen_eleaning/view/login/otp_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase (automatic initialization is disabled in AndroidManifest.xml)
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Set system UI overlay style based on current platform brightness
  final Brightness platformBrightness =
      WidgetsBinding.instance.platformDispatcher.platformBrightness;
  final bool isDark = platformBrightness == Brightness.dark;
  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
      statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
    ),
  );

  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Define theme colors
    const Color primaryColor = Color.fromARGB(
      255,
      166,
      33,
      243,
    ); // Purple color from onboarding
    const Color secondaryColor = Color.fromARGB(
      255,
      17,
      17,
      17,
    ); // Black color from buttons

    return MaterialApp(
      theme: ThemeData(
        primaryColor: primaryColor,
        scaffoldBackgroundColor: Colors.white,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: secondaryColor,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
          ),
        ),
        colorScheme: const ColorScheme.light(
          primary: primaryColor,
          secondary: secondaryColor,
          surface: Colors.white,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: secondaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        useMaterial3: true,
      ),
      darkTheme: ThemeData(
        primaryColor: primaryColor,
        // ### Dark mode screen background: change this to set the overall dark background
        scaffoldBackgroundColor: Colors.grey[900],
        appBarTheme: AppBarTheme(
          // ### Dark mode AppBar background color
          backgroundColor: Colors.grey[900],
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
        ),
        colorScheme: ColorScheme.dark(
          primary: primaryColor,
          secondary: secondaryColor,
          // ### Dark mode surface color (cards, sheets, BottomAppBar background)
          surface: Colors.grey[850]!,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        useMaterial3: true,
      ),
      themeMode: ThemeMode.system, // This will follow the device theme
      debugShowCheckedModeBanner: false,
      initialRoute: '/',
      routes: {
        '/': (context) => const AppScaffold(child: AuthWrapper()),
        '/onboarding': (context) => const OnboardingScreen(),
        '/home': (context) => const AppScaffold(child: HomeScreen()),
        '/signup': (context) => const SignupScreen(),
        '/login': (context) => const LoginScreen(),
        '/phone-signup': (context) => const PhoneSignUpScreen(),
        '/profile': (context) => const ProfileScreen(),
        '/backend-test': (context) => const BackendTestScreen(),
      },
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/course-details':
            final courseId = settings.arguments as String;
            return MaterialPageRoute(
              builder: (context) => CourseDetailsScreen(courseId: courseId),
            );
          case '/enrollment':
            final courseId = settings.arguments as String;
            return MaterialPageRoute(
              builder: (context) => EnrollmentScreen(courseId: courseId),
            );
          case '/checkout':
            final enrollmentData = settings.arguments as Map<String, dynamic>;
            return MaterialPageRoute(
              builder: (context) =>
                  CheckoutScreen(enrollmentData: enrollmentData),
            );
          default:
            return null;
        }
      },
    );
  }
}

class AppScaffold extends StatefulWidget {
  final Widget child;

  const AppScaffold({super.key, required this.child});

  @override
  State<AppScaffold> createState() => _AppScaffoldState();
}

class _AppScaffoldState extends State<AppScaffold> {
  int _selectedIndex = 0; // Default to Home tab

  final List<Widget> _screens = [
    const HomeScreen(),
    const Center(child: Text('Search Coming Soon')),
    const Center(child: Text('Sparks Coming Soon')),
    const Center(child: Text('Downloads Coming Soon')),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    // Only show bottom navigation on authenticated screens
    final bool showBottomNav = ModalRoute.of(context)?.settings.name == '/home';

    return PopScope(
      canPop: !(showBottomNav && _selectedIndex != 0),
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          setState(() {
            _selectedIndex = 0;
          });
        }
      },
      child: Scaffold(
        body: AnnotatedRegion<SystemUiOverlayStyle>(
          value: Theme.of(context).brightness == Brightness.dark
              ? const SystemUiOverlayStyle(
                  statusBarColor: Colors.transparent,
                  statusBarIconBrightness: Brightness.light,
                  statusBarBrightness: Brightness.dark,
                )
              : const SystemUiOverlayStyle(
                  statusBarColor: Colors.transparent,
                  statusBarIconBrightness: Brightness.dark,
                  statusBarBrightness: Brightness.light,
                ),
          child: showBottomNav
              ? IndexedStack(index: _selectedIndex, children: _screens)
              : widget.child,
        ),
        bottomNavigationBar: showBottomNav
            ? BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                backgroundColor: const Color(0xFF111111),
                selectedItemColor: Colors.white,
                unselectedItemColor: Colors.white70,
                showUnselectedLabels: true,
                selectedFontSize: 12,
                unselectedFontSize: 12,
                currentIndex: _selectedIndex,
                onTap: (index) {
                  setState(() {
                    _selectedIndex = index;
                  });
                },
                items: const [
                  BottomNavigationBarItem(
                    icon: Icon(CupertinoIcons.house),
                    label: 'Home',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(CupertinoIcons.search),
                    label: 'Search',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(CupertinoIcons.tv),
                    label: 'Live Classes',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(CupertinoIcons.heart_fill),
                    label: 'Wishlist',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(CupertinoIcons.person_crop_circle),
                    label: 'Profile',
                  ),
                ],
              )
            : null,
      ),
    );
  }
}
