import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class TrailerDetailsScreen extends StatefulWidget {
  final Map<String, String> showData;

  const TrailerDetailsScreen({super.key, required this.showData});

  @override
  State<TrailerDetailsScreen> createState() => _TrailerDetailsScreenState();
}

class _TrailerDetailsScreenState extends State<TrailerDetailsScreen> {
  int _selectedSeason = 1;
  String _selectedLanguage = 'Hindi';

  final List<String> _languages = [
    'Hindi',
    'Tamil',
    'Bengali',
    'Marathi',
    'Kannada',
  ];

  final List<String> _genres = [
    'Action',
    'Thriller',
    'Crime',
    'Spy',
    'Political',
    'Covert Agencies',
  ];

  final List<Map<String, String>> _episodes = [
    {
      'title': 'Kaagaz Ke Phool',
      'episode': 'S1 E1',
      'date': '17 Mar 2020',
      'duration': '50m',
      'image': 'https://picsum.photos/300/200?random=10',
    },
    {
      'title': 'Guide',
      'episode': 'S1 E2',
      'date': '17 Mar 2020',
      'duration': '45m',
      'image': 'https://picsum.photos/300/200?random=11',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D0F12),
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildVideoSection(),
                      const SizedBox(height: 16),
                      _buildLanguageSelector(),
                      const SizedBox(height: 16),
                      _buildShowInfo(),
                      const SizedBox(height: 16),
                      _buildWatchButton(),
                      const SizedBox(height: 16),
                      _buildGenres(),
                      const SizedBox(height: 12),
                      _buildDescription(),
                      const SizedBox(height: 16),
                      _buildActionButtons(),
                      const SizedBox(height: 16),
                      _buildSeasonTabs(),
                      const SizedBox(height: 12),
                      _buildEpisodeList(),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'TRAILER',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.close, color: Colors.white, size: 24),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoSection() {
    return Container(
      height: 220,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black26,
        borderRadius: BorderRadius.circular(12),
        image: DecorationImage(
          image: NetworkImage(widget.showData['image'] ?? ''),
          fit: BoxFit.cover,
        ),
      ),
      child: const Center(
        child: Icon(Icons.play_circle_fill, color: Colors.white, size: 80),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return SizedBox(
      height: 36,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: _languages.length,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final language = _languages[index];
          final isSelected = language == _selectedLanguage;
          return GestureDetector(
            onTap: () => setState(() => _selectedLanguage = language),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(18),
                border: Border.all(
                  color: isSelected ? Colors.white : Colors.white24,
                  width: 1,
                ),
              ),
              child: Text(
                language,
                style: GoogleFonts.poppins(
                  color: isSelected ? Colors.black : Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShowInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'hotstar specials',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ShaderMask(
          shaderCallback: (bounds) => const LinearGradient(
            colors: [Color(0xFFECE94A), Color(0xFFA1FFCE)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ).createShader(bounds),
          child: Text(
            'SPECIAL OPS',
            style: GoogleFonts.orbitron(
              color: Colors.white,
              fontWeight: FontWeight.w800,
              fontSize: 28,
              letterSpacing: 1.2,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'New Season',
          style: GoogleFonts.poppins(
            color: const Color(0xFF1E90FF),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '2025 • U/A 16+ • 2 Seasons • 7 Languages',
          style: GoogleFonts.poppins(color: Colors.white70, fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildWatchButton() {
    return SizedBox(
      width: double.infinity,
      height: 52,
      child: ElevatedButton(
        onPressed: () {
          // Handle watch episode action
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Watch Episode functionality coming soon!'),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.play_arrow, size: 28),
            const SizedBox(width: 8),
            Text(
              'Watch Now',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenres() {
    return Wrap(
      spacing: 12,
      runSpacing: 6,
      children: _genres.map((genre) {
        return Text(
          genre,
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 13,
            fontWeight: FontWeight.w500,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDescription() {
    return Text(
      'An assassination and a kidnap spark the beginning of an insidious war that can bring the nation to its knees. Himmat Singh is back, and the enemy is everywhere.',
      style: GoogleFonts.poppins(
        color: Colors.white70,
        fontSize: 14,
        height: 1.5,
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(Icons.add, 'Watchlist'),
        _buildActionButton(Icons.share, 'Share'),
        _buildActionButton(Icons.star_border, 'Rate'),
      ],
    );
  }

  Widget _buildActionButton(IconData icon, String label) {
    return GestureDetector(
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$label functionality coming soon!')),
        );
      },
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 28),
          const SizedBox(height: 6),
          Text(
            label,
            style: GoogleFonts.poppins(color: Colors.white70, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildSeasonTabs() {
    return Row(
      children: [
        _buildSeasonTab(1),
        const SizedBox(width: 32),
        _buildSeasonTab(2),
      ],
    );
  }

  Widget _buildSeasonTab(int season) {
    final isSelected = season == _selectedSeason;
    return GestureDetector(
      onTap: () => setState(() => _selectedSeason = season),
      child: Column(
        children: [
          Text(
            'Season $season',
            style: GoogleFonts.poppins(
              color: isSelected ? Colors.white : Colors.white54,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 6),
          if (isSelected) Container(height: 3, width: 70, color: Colors.white),
        ],
      ),
    );
  }

  Widget _buildEpisodeList() {
    return Column(
      children: _episodes.map((episode) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: SizedBox(
                  width: 140,
                  height: 80,
                  child: Image.network(
                    episode['image']!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stack) =>
                        Container(color: Colors.black26),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      episode['title']!,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${episode['episode']} • ${episode['date']} • ${episode['duration']}',
                      style: GoogleFonts.poppins(
                        color: Colors.white54,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.download_outlined,
                color: Colors.white54,
                size: 24,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
