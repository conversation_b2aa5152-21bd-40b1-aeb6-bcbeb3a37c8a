{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 04:39:27'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 04:39:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-08 04:39:30'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 04:39:30'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 04:39:30'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 04:39:30'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 06:13:01'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 06:13:03'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-08 06:13:04'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 06:13:04'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 06:13:04'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 06:13:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '6a8db732-b3b4-4f62-8e0a-ee8677a01b0f',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 06:17:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '977dc868-06ed-4e0a-aea3-03945f7663d4',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 06:17:30'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 06:17:31'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1745ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 06:17:32'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 06:17:32'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '1702ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 06:17:32'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:16:01'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:16:03'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-08 07:16:03'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:16:03'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:16:03'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:16:03'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '989312ba-b549-4b3e-853a-2b09c4580c32',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:19:42'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '8a5965ed-6f7b-4c12-953e-c04819097478',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:19:42'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'b676bb18-0fe2-42cd-bb73-cc433a0e1d9e',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:19:42'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:19:43'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1813ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:19:44'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:19:44'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:19:44'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '2028ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:19:44'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2032ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:19:44'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '074f4616-becd-495d-895b-317dc7c5d17c',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:29:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '8589b6cf-e7d4-4824-a598-2ea111805930',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:29:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '340d4cfe-7a6e-4538-9b04-111e7d150f7f',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:29:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '279217e5-f29f-49b8-a5f8-a4f970cf00cb',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:29:56'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '178af5a8-c69c-4dbd-9c13-81bdde652def',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:29:56'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:29:58'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:29:58'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:29:58'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:29:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '1842ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:29:58'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:29:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1905ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:29:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '2099ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:29:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2097ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:29:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2195ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:29:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '6b727f4f-b304-40ed-b5fb-ed64581ceaca',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:33:46'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '4ad3f01c-9de2-43e0-aa10-47c8c9da42cd',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:33:46'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'ccb2a437-53f6-4744-a191-2958a3e3488d',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 07:33:46'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:33:48'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:33:48'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 07:33:48'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2457ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:33:49'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2197ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:33:49'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '2235ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 07:33:49'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '96529da7-e8ac-4ada-a754-fce3b3c5ad9e',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:08:48'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'eb4c4e3f-b9fb-4c34-9bcf-c2850d3ffe4f',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:08:48'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:08:49'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:08:49'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1943ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:08:49'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '1709ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:08:50'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '904bf0b7-d5c0-4b17-ba6b-10451fe9ccd1',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:44:14'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:44:15'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2158ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:44:16'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'e64e6e79-9ae3-4b95-8fa2-0f05c8f29c1b',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:45:01'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:45:03'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1944ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:45:03'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'fd0d82ff-d0b9-4122-a073-2525ad7f19d7',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:51:05'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:51:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2205ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:51:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '93fc51eb-f7fb-4ebf-a530-cced977c787c',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:53:03'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '22d459aa-9444-4e2e-b441-529e8c6bdde8',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:53:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'e6f7e93b-68a4-42e2-a4f5-ba5655c9b71b',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:53:04'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:53:05'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:53:05'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:53:05'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '1894ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:53:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2217ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:53:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2022ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:53:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '228b1fa0-fe34-4cb5-bad1-c34b67363284',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:54:31'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:54:33'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1752ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:54:33'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '6c289730-113a-48c0-8d69-8270803713f9',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 09:56:03'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 09:56:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1826ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 09:56:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '2f9d741f-a94e-4b64-ab04-9c32cb59d0b2',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:01:41'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:01:43'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2249ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:01:43'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '1628d698-55bd-4e70-8ed9-dea175f1fc27',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:02:33'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:02:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1789ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:02:35'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '3e003228-198a-4366-a305-6804a6d06223',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:03:34'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:03:36'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1754ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:03:36'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '4415e322-1cb0-4231-812c-2d3da2d023af',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:15:02'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '15ff8af3-48b1-4eef-9471-7a131f50b242',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:15:03'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:15:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1939ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:15:04'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:15:05'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2135ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:15:05'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '90272f20-dc21-4e8f-816f-87d35467bca6',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:16:57'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '2d52f774-a0c2-41a6-9a82-d475a3d311f7',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:16:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'ab641337-b07a-43f7-bd97-27a9ff11b72a',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:16:58'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:16:59'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1799ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:16:59'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:16:59'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:16:59'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '1778ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:16:59'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2054ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:17:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '0e3a4ae6-6a8b-4ae6-ad61-79c567e95f42',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:24:26'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:24:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2167ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:24:28'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'fe7a70d1-f5e6-47ca-aa08-5c58c3f81e10',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:37:36'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:37:38'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2241ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:37:39'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '1a445a24-2f9d-47bc-ab64-a382d1b6034f',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 10:46:48'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 10:46:50'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1762ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 10:46:50'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '0f5f90fa-7b8a-481b-a8af-02d741de3950',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:15:20'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:15:22'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2340ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:15:23'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:28:01'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:28:21'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:28:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-08 11:28:23'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:28:23'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:28:23'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:28:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '10ccf7aa-dd8a-40d7-b7dc-39b949adb466',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:48:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '80339f0d-b349-48f3-9b57-4130588cc796',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:48:05'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '4ce3f5c4-8894-481b-af17-cb4bdddea76b',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:48:05'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:48:06'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:48:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1945ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:48:06'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:48:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '1749ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:48:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '1947ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:48:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '9039864d-cd98-4871-b960-d2c0f0e75dd9',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:53:25'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '9df5fed5-c758-483a-9558-e9798956c124',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:53:25'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'df48f504-d48e-4405-abf2-fa6d39afb7eb',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:53:25'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:53:26'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:53:27'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:53:27'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '1783ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:53:27'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '1820ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:53:27'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1853ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:53:27'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'bab5e4e1-a1bb-4c1b-be8b-8cdacb27b465',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:53:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'b898096d-8430-4aa0-8796-d7eb7dcc693f',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:53:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'f884f7f7-e6dc-401e-b05a-1e87aee42a7c',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:53:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '5a5787eb-6083-426a-b6a8-8285817e892d',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:53:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '1efbb094-7386-41c6-8a53-8421e20da10e',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 11:53:29'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '305ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:53:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '327ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:53:30'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '319ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:53:30'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:53:31'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 11:53:31'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '1962ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:53:31'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2004ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 11:53:32'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 12:26:35'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 12:26:37'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-08 12:26:37'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 12:26:37'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 12:26:37'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 12:26:37'
}
{
  message: 'Firebase Admin SDK initialized successfully',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:01:20'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:01:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'info',
  message: 'Database connected successfully at:',
  timestamp: '2025-08-08 13:01:23'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:01:23'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:01:23'
}
{
  message: 'Health check available at: http://localhost:3001/health',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:01:23'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '389b4042-1313-41c7-a766-71ca580c2ceb',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 13:02:15'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:02:17'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '2088ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 13:02:18'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '5be78104-463c-4c8d-8628-8988c2ca7a71',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 13:21:04'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '8c163bab-a2da-42af-b6a3-5b5811c52981',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 13:21:05'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '66aa9a10-321f-436f-9297-19ec77d961db',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 13:21:05'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:21:06'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1837ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 13:21:06'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:21:07'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:21:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '1768ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 13:21:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '2076ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 13:21:07'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '75b72eb7-b5b7-4d43-a7db-4233adface1a',
  method: 'GET',
  url: '/api/courses/categories',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 13:55:58'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: 'f5879584-c65e-402a-ba52-486bc44b62e5',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 13:55:59'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  requestId: '910a0cbd-1dac-49ab-9461-d49bbe70ee43',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Dart/3.8 (dart:io)',
  contentType: 'application/json',
  contentLength: undefined,
  level: 'info',
  message: 'Request started',
  timestamp: '2025-08-08 13:55:59'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:56:00'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/categories',
  statusCode: 200,
  responseTime: '1829ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 13:56:00'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:56:00'
}
{
  message: 'New database connection established',
  level: 'info',
  service: 'quipgen-elearning-backend',
  environment: 'development',
  timestamp: '2025-08-08 13:56:01'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/featured?limit=6',
  statusCode: 200,
  responseTime: '1773ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 13:56:01'
}
{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  method: 'GET',
  url: '/api/courses/popular?limit=6',
  statusCode: 200,
  responseTime: '1777ms',
  userAgent: 'Dart/3.8 (dart:io)',
  ip: '::ffff:127.0.0.1',
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-08-08 13:56:01'
}
